<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\InformationArticleController;

// Authentication routes would go here if needed

// API routes for React PWA (read-only)
Route::prefix('v1')->group(function () {
    // Products API
    Route::get('/products', [ProductController::class, 'apiIndex']);
    Route::get('/products/{product}', [ProductController::class, 'apiShow']);

    // Information Articles API
    Route::get('/information', [InformationArticleController::class, 'apiIndex']);
    Route::get('/information/{informationArticle:slug}', [InformationArticleController::class, 'apiShow']);
    Route::get('/information-categories', [InformationArticleController::class, 'apiCategories']);
    Route::post('/information/{informationArticle:slug}/like', [InformationArticleController::class, 'apiLike']);
});
