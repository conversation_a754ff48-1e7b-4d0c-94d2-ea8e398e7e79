import { useState, useEffect } from 'react'
import './App.css'
import Homepage from './components/Homepage'
import Navigation from './components/Navigation'
import Profile from './components/Profile'
import Calendar from './components/Calendar'
import Produk from './components/Produk'
import Layanan from './components/Layanan'
import Information from './components/Information'
import InformationEnhanced from './components/InformationEnhanced'
import InvoiceList from './components/InvoiceList'
import PurchaseOrderList from './components/PurchaseOrderList'
import { storage } from './utils/storage'

/**
 * Komponen utama aplikasi PPWA
 * Mengintegrasikan Homepage dan Navigation dengan Capacitor untuk deployment Android
 * Termasuk offline functionality dan device information
 */
function App() {
  // State untuk mengontrol tab navigasi aktif
  const [activeTab, setActiveTab] = useState('home');

  // Initialize app services
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize storage
        await storage.init();
        console.log('[App] Storage initialized');

        // Register service worker
        if ('serviceWorker' in navigator) {
          const registration = await (navigator.serviceWorker as any).register('/sw.js');
          console.log('[App] Service Worker registered:', registration);
        }

        console.log('[App] App initialization completed');
      } catch (error: any) {
        console.error('[App] Error initializing app:', error);
      }
    };

    // Handle custom navigation events from Calendar FAB
    const handleNavigateToHome = () => {
      setActiveTab('home');
    };

    // Handle custom navigation events from Homepage stats cards
    const handleNavigateToPage = (event: CustomEvent) => {
      setActiveTab(event.detail);
    };

    window.addEventListener('navigateToHome', handleNavigateToHome);
    window.addEventListener('navigateToPage', handleNavigateToPage as EventListener);

    initializeApp();

    // Cleanup
    return () => {
      window.removeEventListener('navigateToHome', handleNavigateToHome);
      window.removeEventListener('navigateToPage', handleNavigateToPage as EventListener);
    };
  }, []);

  /**
   * Fungsi untuk menangani perubahan tab navigasi
   * @param tabId - ID tab yang dipilih
   */
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    console.log(`Navigasi tab: ${tabId}`);
    // TODO: Implementasi routing atau perubahan konten berdasarkan tab
  };

  /**
   * Fungsi untuk merender konten berdasarkan tab aktif
   * @returns JSX Element sesuai dengan tab yang dipilih
   */
  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return <Homepage />;
      case 'kalender':
        return <Calendar />;
      case 'profile':
        return <Profile />;
      case 'information':
        return <InformationEnhanced />;
      case 'produk':
        return <Produk />;
      case 'layanan':
        return <Layanan />;
      case 'invoice-list':
        return <InvoiceList />;
      case 'purchase-order-list':
        return <PurchaseOrderList />;
      default:
        return <Homepage />;
    }
  };

  return (
    <div className="app-container h-screen bg-base-100 overflow-hidden" data-theme="ppwa">
      {/* Konten utama berdasarkan tab aktif */}
      <main className="flex-1 overflow-auto">
        {renderContent()}
      </main>

      {/* Navigasi bawah - only show on home page */}
      {activeTab === 'home' && (
        <Navigation
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
      )}
    </div>
  )
}

export default App
