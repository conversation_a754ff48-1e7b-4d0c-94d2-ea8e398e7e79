/**
 * TypeScript interfaces untuk aplikasi PPWA
 */

/**
 * Interface untuk data layanan
 */
export interface LayananItem {
  id: string;
  title: string;
  image: string;
  description: string;
  features: string[];
}

/**
 * Interface untuk data informasi (legacy - untuk backward compatibility)
 */
export interface InformationItem {
  id: string;
  title: string;
  image: string;
  description: string;
  content: string;
  date: string;
  category: string;
}

/**
 * Interface untuk data artikel informasi dari API
 */
export interface InformationArticle {
  id: number;
  title: string;
  excerpt?: string;
  content: string;
  featured_image?: string;
  image_alt_text?: string;
  author?: string;
  published_at: string;
  category: string;
  tags?: string[];
  status: 'draft' | 'published' | 'archived';
  is_featured: boolean;
  priority: number;
  slug: string;
  meta_title?: string;
  meta_description?: string;
  view_count: number;
  like_count: number;
  created_at: string;
  updated_at: string;
  formatted_published_date?: string;
  reading_time?: number;
}

/**
 * Interface untuk props komponen Layanan
 */
export interface LayananProps {
  className?: string;
}

/**
 * Interface untuk props komponen Information
 */
export interface InformationProps {
  className?: string;
}

/**
 * Interface untuk navigation event
 */
export interface NavigationEvent extends CustomEvent {
  type: 'navigateToHome';
}

/**
 * Interface untuk kategori informasi
 */
export interface InformationCategory {
  id: string;
  name: string;
  color: string;
}

/**
 * Interface untuk meta informasi
 */
export interface MetaInfo {
  date: string;
  category: string;
  author?: string;
  tags?: string[];
}

/**
 * Interface untuk layanan feature
 */
export interface LayananFeature {
  id: string;
  text: string;
  icon?: string;
  highlighted?: boolean;
}

/**
 * Interface untuk extended layanan item dengan features yang lebih detail
 */
export interface ExtendedLayananItem extends Omit<LayananItem, 'features'> {
  features: LayananFeature[];
  price?: string;
  duration?: string;
  availability?: boolean;
}

/**
 * Interface untuk extended information item dengan metadata
 */
export interface ExtendedInformationItem extends InformationItem {
  meta: MetaInfo;
  readTime?: number;
  views?: number;
  likes?: number;
}

/**
 * Interface untuk search dan filter
 */
export interface SearchFilter {
  query: string;
  category?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sortBy?: 'date' | 'title' | 'category' | 'views';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Interface untuk responsive breakpoints
 */
export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  largeDesktop: number;
}

/**
 * Interface untuk theme configuration
 */
export interface ThemeConfig {
  primary: string;
  secondary: string;
  accent: string;
  neutral: string;
  base: string;
  info: string;
  success: string;
  warning: string;
  error: string;
}

/**
 * Interface untuk animation configuration
 */
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
  stagger?: number;
}

/**
 * Interface untuk component state
 */
export interface ComponentState {
  loading: boolean;
  error: string | null;
  data: any;
  lastUpdated: Date;
}

/**
 * Interface untuk API response
 */
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp: Date;
}

/**
 * Interface untuk pagination
 */
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Interface untuk API pagination response
 */
export interface ApiPagination {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  has_more: boolean;
}

/**
 * Interface untuk Information Articles API response
 */
export interface InformationArticlesApiResponse {
  success: boolean;
  data: InformationArticle[];
  pagination?: ApiPagination;
}

/**
 * Interface untuk single Information Article API response
 */
export interface InformationArticleApiResponse {
  success: boolean;
  data: InformationArticle;
  message?: string;
}

/**
 * Interface untuk categories API response
 */
export interface CategoriesApiResponse {
  success: boolean;
  data: string[];
}

/**
 * Interface untuk paginated response
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: Pagination;
}

/**
 * Interface untuk data invoice
 */
export interface InvoiceItem {
  id: string;
  no: number;
  name: string;
  poNumber: string;
  unit: string;
  status: 'Open' | 'Paid' | 'Overdue' | 'Cancelled';
  tanggalInvoice: string;
  total: string;
  pembayaran: string;
  sisa: string;
}

/**
 * Interface untuk data purchase order
 */
export interface PurchaseOrderItem {
  id: string;
  no: number;
  mrNumber: string;
  unit: string;
  status: 'Ready WO' | 'Diajukan' | 'Approved' | 'Rejected';
  tanggalMr: string;
  total: string;
}

/**
 * Interface untuk props komponen InvoiceList
 */
export interface InvoiceListProps {
  className?: string;
}

/**
 * Interface untuk props komponen PurchaseOrderList
 */
export interface PurchaseOrderListProps {
  className?: string;
}
